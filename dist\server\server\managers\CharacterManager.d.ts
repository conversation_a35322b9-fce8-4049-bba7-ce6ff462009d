import { DatabaseManager } from '../core/DatabaseManager';
import { Character } from '../../types/index';
export declare class CharacterManager {
    private logger;
    private database;
    constructor(database: DatabaseManager);
    handleCreate(data: {
        player: any;
        data: Partial<Character>;
    }): Promise<void>;
    handleSelect(data: {
        player: any;
        data: {
            characterId: string;
        };
    }): Promise<void>;
    handleUpdate(data: {
        player: any;
        data: {
            characterId: string;
            updates: Partial<Character>;
        };
    }): Promise<void>;
    private generateCharacterId;
    private getDefaultAppearance;
    private getDefaultStats;
    private getDefaultPosition;
    private loadCharacter;
    private updateCharacter;
    save<PERSON><PERSON>cter(character: Character): Promise<boolean>;
    delete<PERSON><PERSON>cter(characterId: string): Promise<boolean>;
    getPlayerCharacters(playerId: string): Promise<Character[]>;
}
