import { EventEmitter } from 'events';
import { EventHandler } from '@types/index';
export declare class EventManager extends EventEmitter {
    private logger;
    private eventHandlers;
    constructor();
    on(eventName: string, handler: EventHandler): this;
    once(eventName: string, handler: EventHandler): this;
    off(eventName: string, handler: EventHandler): this;
    emit(eventName: string, ...args: any[]): boolean;
    emitAsync(eventName: string, data?: any): Promise<void>;
    getRegisteredEvents(): string[];
    getHandlerCount(eventName: string): number;
    removeAllListeners(eventName?: string): this;
    getStats(): {
        totalEvents: number;
        totalHandlers: number;
        events: Record<string, number>;
    };
    use(eventName: string, middleware: EventHandler): this;
}
