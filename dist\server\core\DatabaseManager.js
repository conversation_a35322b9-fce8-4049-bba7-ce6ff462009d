"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const Logger_1 = require("../utils/Logger");
class DatabaseManager {
    constructor(config) {
        this.isConnected = false;
        this.logger = new Logger_1.Logger('DatabaseManager');
        this.config = config;
    }
    async connect() {
        if (this.isConnected) {
            this.logger.warn('Database is already connected');
            return;
        }
        try {
            const dbConfig = this.config.getDatabaseConfig();
            // Configure mongoose options
            const options = {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                bufferCommands: false,
                bufferMaxEntries: 0,
            };
            this.logger.info('Connecting to database...');
            await mongoose_1.default.connect(dbConfig.url, options);
            this.isConnected = true;
            this.logger.success(`Connected to database: ${dbConfig.name}`);
            // Set up connection event listeners
            this.setupEventListeners();
        }
        catch (error) {
            this.logger.error('Failed to connect to database:', error);
            throw error;
        }
    }
    async disconnect() {
        if (!this.isConnected) {
            this.logger.warn('Database is not connected');
            return;
        }
        try {
            await mongoose_1.default.disconnect();
            this.isConnected = false;
            this.logger.info('Disconnected from database');
        }
        catch (error) {
            this.logger.error('Error disconnecting from database:', error);
            throw error;
        }
    }
    setupEventListeners() {
        mongoose_1.default.connection.on('error', (error) => {
            this.logger.error('Database connection error:', error);
        });
        mongoose_1.default.connection.on('disconnected', () => {
            this.logger.warn('Database disconnected');
            this.isConnected = false;
        });
        mongoose_1.default.connection.on('reconnected', () => {
            this.logger.info('Database reconnected');
            this.isConnected = true;
        });
        mongoose_1.default.connection.on('close', () => {
            this.logger.info('Database connection closed');
            this.isConnected = false;
        });
    }
    getConnection() {
        if (!this.isConnected) {
            throw new Error('Database is not connected');
        }
        return mongoose_1.default.connection;
    }
    isHealthy() {
        return this.isConnected && mongoose_1.default.connection.readyState === 1;
    }
    async ping() {
        try {
            if (!this.isConnected) {
                return false;
            }
            await mongoose_1.default.connection.db.admin().ping();
            return true;
        }
        catch (error) {
            this.logger.error('Database ping failed:', error);
            return false;
        }
    }
}
exports.DatabaseManager = DatabaseManager;
