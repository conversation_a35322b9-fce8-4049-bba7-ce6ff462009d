import mongoose from 'mongoose';
import { ConfigManager } from './ConfigManager';
import { Logger } from '../utils/Logger';

export class DatabaseManager {
  private logger: Logger;
  private config: ConfigManager;
  private isConnected = false;

  constructor(config: ConfigManager) {
    this.logger = new Logger('DatabaseManager');
    this.config = config;
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      this.logger.warn('Database is already connected');
      return;
    }

    try {
      const dbConfig = this.config.getDatabaseConfig();
      
      // Configure mongoose options
      const options = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferCommands: false,
        bufferMaxEntries: 0,
      };

      this.logger.info('Connecting to database...');
      
      await mongoose.connect(dbConfig.url, options);
      
      this.isConnected = true;
      this.logger.success(`Connected to database: ${dbConfig.name}`);

      // Set up connection event listeners
      this.setupEventListeners();

    } catch (error) {
      this.logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      this.logger.warn('Database is not connected');
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      this.logger.info('Disconnected from database');
    } catch (error) {
      this.logger.error('Error disconnecting from database:', error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    mongoose.connection.on('error', (error) => {
      this.logger.error('Database connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      this.logger.warn('Database disconnected');
      this.isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      this.logger.info('Database reconnected');
      this.isConnected = true;
    });

    mongoose.connection.on('close', () => {
      this.logger.info('Database connection closed');
      this.isConnected = false;
    });
  }

  getConnection() {
    if (!this.isConnected) {
      throw new Error('Database is not connected');
    }
    return mongoose.connection;
  }

  isHealthy(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  async ping(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }
      
      await mongoose.connection.db?.admin().ping();
      return true;
    } catch (error) {
      this.logger.error('Database ping failed:', error);
      return false;
    }
  }
}
