# Realistic RageMP Roleplay Server

A highly realistic RageMP roleplay server built with TypeScript and React, featuring exceptional UI design and clean, organized code architecture.

## 🎯 Project Goals

- **Exceptional UI Design**: Custom, polished interfaces that stand out from typical roleplay servers
- **Realistic Roleplay**: Immersive and authentic roleplay experience
- **Clean Architecture**: Well-organized, maintainable codebase with TypeScript
- **Modern Tech Stack**: React for UI, TypeScript for type safety, MongoDB for data persistence

## 🚀 Features

### Authentication System
- Email/Username/Password registration
- Discord OAuth integration
- Google OAuth integration
- Gmail SMTP email verification with 6-digit codes
- "Remember me" functionality
- Modern, attractive UI design

### Character System
- One character per account limit
- Detailed character creation and customization
- Realistic character progression
- Immersive character management

### Core Systems
- Advanced inventory system
- Realistic economy and banking
- Job system with progression
- Housing and property management
- Vehicle system with realistic mechanics
- Emergency services integration
- Government and legal systems

## 🛠️ Tech Stack

### Server-Side
- **TypeScript**: Type-safe server development
- **Node.js**: Runtime environment
- **MongoDB**: Database for persistent data
- **Express**: Web framework for API endpoints
- **Socket.IO**: Real-time communication
- **Passport**: Authentication middleware
- **Nodemailer**: Email functionality

### Client-Side
- **React**: Modern UI framework
- **TypeScript**: Type-safe client development
- **Styled Components**: CSS-in-JS styling
- **Framer Motion**: Smooth animations
- **React Hook Form**: Form management
- **React Query**: Data fetching and caching

### Development Tools
- **Webpack**: Module bundling
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Jest**: Testing framework
- **Nodemon**: Development server

## 📁 Project Structure

```
src/
├── client/                 # React client-side code
│   ├── components/         # Reusable UI components
│   ├── pages/             # Page components
│   ├── providers/         # Context providers
│   ├── hooks/             # Custom React hooks
│   ├── services/          # API services
│   ├── styles/            # Global styles and themes
│   └── utils/             # Client utilities
├── server/                # Node.js server-side code
│   ├── core/              # Core server functionality
│   ├── managers/          # Business logic managers
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── middleware/        # Express middleware
│   ├── services/          # External services
│   └── utils/             # Server utilities
├── shared/                # Shared code between client/server
│   ├── constants.ts       # Shared constants
│   ├── types.ts           # Shared TypeScript types
│   └── utils.ts           # Shared utilities
└── types/                 # TypeScript type definitions
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- MongoDB 5+
- RageMP Server

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ragemp-realistic-roleplay
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

### Development Commands

- `npm run dev` - Start both server and client in development mode
- `npm run dev:server` - Start only the server in development mode
- `npm run dev:client` - Start only the client in development mode
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- **Database**: MongoDB connection string
- **JWT**: Secret keys for authentication
- **Email**: Gmail SMTP configuration
- **OAuth**: Discord and Google OAuth credentials
- **Server**: Port and URL configurations

### RageMP Integration

The server integrates with RageMP through:
- Event handlers for player connections
- Custom client-server communication
- Resource management
- Player data synchronization

## 🎨 UI Design Philosophy

- **Modern & Clean**: Contemporary design with clean lines and intuitive layouts
- **Immersive**: UI elements that enhance the roleplay experience
- **Responsive**: Adaptive design that works across different screen sizes
- **Accessible**: Following accessibility best practices
- **Performance**: Optimized for smooth 60fps gameplay

## 🧪 Testing

The project includes comprehensive testing:

- **Unit Tests**: Individual component and function testing
- **Integration Tests**: Testing component interactions
- **E2E Tests**: End-to-end user flow testing
- **API Tests**: Server endpoint testing

Run tests with:
```bash
npm test
```

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Component Documentation](docs/components.md)
- [Database Schema](docs/database.md)
- [Deployment Guide](docs/deployment.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- RageMP team for the multiplayer framework
- React team for the excellent UI library
- TypeScript team for type safety
- All contributors and community members
