"use strict";
// Shared Constants for RageMP Roleplay Server
Object.defineProperty(exports, "__esModule", { value: true });
exports.REGEX_PATTERNS = exports.HTTP_STATUS = exports.NOTIFICATION_TYPES = exports.SUCCESS_MESSAGES = exports.ERROR_MESSAGES = exports.CHARACTER_LIMITS = exports.GAME_CONSTANTS = exports.UI_COMPONENTS = exports.CLIENT_EVENTS = exports.SERVER_EVENTS = void 0;
// Server Events
exports.SERVER_EVENTS = {
    // Authentication
    AUTH_LOGIN: 'auth:login',
    AUTH_REGISTER: 'auth:register',
    AUTH_LOGOUT: 'auth:logout',
    AUTH_VERIFY_EMAIL: 'auth:verify-email',
    AUTH_RESEND_VERIFICATION: 'auth:resend-verification',
    AUTH_FORGOT_PASSWORD: 'auth:forgot-password',
    AUTH_RESET_PASSWORD: 'auth:reset-password',
    AUTH_REFRESH_TOKEN: 'auth:refresh-token',
    // Player Management
    PLAYER_CONNECT: 'player:connect',
    PLAYER_DISCONNECT: 'player:disconnect',
    PLAYER_SPAWN: 'player:spawn',
    PLAYER_UPDATE: 'player:update',
    PLAYER_SAVE: 'player:save',
    // Character Management
    CHARACTER_CREATE: 'character:create',
    CHARACTER_SELECT: 'character:select',
    CHARACTER_UPDATE: 'character:update',
    CHARACTER_DELETE: 'character:delete',
    CHARACTER_SAVE: 'character:save',
    // UI Management
    UI_SHOW: 'ui:show',
    UI_HIDE: 'ui:hide',
    UI_UPDATE: 'ui:update',
    UI_TOGGLE: 'ui:toggle',
    // Notifications
    NOTIFICATION_SHOW: 'notification:show',
    NOTIFICATION_HIDE: 'notification:hide',
    NOTIFICATION_ACTION: 'notification:action',
    // Chat System
    CHAT_MESSAGE: 'chat:message',
    CHAT_COMMAND: 'chat:command',
    CHAT_OOC: 'chat:ooc',
    CHAT_LOCAL: 'chat:local',
    CHAT_WHISPER: 'chat:whisper',
    CHAT_SHOUT: 'chat:shout',
    // Inventory System
    INVENTORY_OPEN: 'inventory:open',
    INVENTORY_CLOSE: 'inventory:close',
    INVENTORY_USE_ITEM: 'inventory:use-item',
    INVENTORY_DROP_ITEM: 'inventory:drop-item',
    INVENTORY_GIVE_ITEM: 'inventory:give-item',
    INVENTORY_UPDATE: 'inventory:update',
    // Vehicle System
    VEHICLE_ENTER: 'vehicle:enter',
    VEHICLE_EXIT: 'vehicle:exit',
    VEHICLE_ENGINE_TOGGLE: 'vehicle:engine-toggle',
    VEHICLE_LOCK_TOGGLE: 'vehicle:lock-toggle',
    VEHICLE_SPAWN: 'vehicle:spawn',
    VEHICLE_DESPAWN: 'vehicle:despawn',
    // Economy System
    ECONOMY_TRANSACTION: 'economy:transaction',
    ECONOMY_ATM_ACCESS: 'economy:atm-access',
    ECONOMY_BANK_TRANSFER: 'economy:bank-transfer',
    ECONOMY_PAY_PLAYER: 'economy:pay-player',
    // Job System
    JOB_START: 'job:start',
    JOB_STOP: 'job:stop',
    JOB_COMPLETE_TASK: 'job:complete-task',
    JOB_UPDATE_PROGRESS: 'job:update-progress',
    // Admin System
    ADMIN_TELEPORT: 'admin:teleport',
    ADMIN_KICK_PLAYER: 'admin:kick-player',
    ADMIN_BAN_PLAYER: 'admin:ban-player',
    ADMIN_UNBAN_PLAYER: 'admin:unban-player',
    ADMIN_SET_MONEY: 'admin:set-money',
    ADMIN_GIVE_ITEM: 'admin:give-item',
};
// Client Events
exports.CLIENT_EVENTS = {
    // UI Events
    UI_READY: 'ui:ready',
    UI_INTERACTION: 'ui:interaction',
    UI_FORM_SUBMIT: 'ui:form-submit',
    UI_BUTTON_CLICK: 'ui:button-click',
    // Input Events
    INPUT_KEY_PRESS: 'input:key-press',
    INPUT_KEY_RELEASE: 'input:key-release',
    INPUT_MOUSE_CLICK: 'input:mouse-click',
    // Camera Events
    CAMERA_CHANGE: 'camera:change',
    CAMERA_RESET: 'camera:reset',
    // Audio Events
    AUDIO_PLAY: 'audio:play',
    AUDIO_STOP: 'audio:stop',
    AUDIO_SET_VOLUME: 'audio:set-volume',
};
// UI Component Types
exports.UI_COMPONENTS = {
    LOGIN: 'login',
    REGISTER: 'register',
    CHARACTER_CREATOR: 'character-creator',
    CHARACTER_SELECTOR: 'character-selector',
    HUD: 'hud',
    INVENTORY: 'inventory',
    CHAT: 'chat',
    PHONE: 'phone',
    ATM: 'atm',
    VEHICLE_HUD: 'vehicle-hud',
    ADMIN_PANEL: 'admin-panel',
    NOTIFICATION: 'notification',
    LOADING: 'loading',
    MENU: 'menu',
    DIALOG: 'dialog',
};
// Game Constants
exports.GAME_CONSTANTS = {
    MAX_PLAYERS: 100,
    MAX_CHARACTERS_PER_PLAYER: 1,
    DEFAULT_SPAWN_POSITION: { x: -1037.8, y: -2737.8, z: 20.2, heading: 0 },
    MAX_INVENTORY_SLOTS: 50,
    MAX_CHAT_MESSAGE_LENGTH: 255,
    MAX_USERNAME_LENGTH: 20,
    MIN_USERNAME_LENGTH: 3,
    MAX_CHARACTER_NAME_LENGTH: 20,
    MIN_CHARACTER_NAME_LENGTH: 2,
    PASSWORD_MIN_LENGTH: 8,
    VERIFICATION_CODE_LENGTH: 6,
    VERIFICATION_CODE_EXPIRY: 15 * 60 * 1000, // 15 minutes
};
// Character Limits
exports.CHARACTER_LIMITS = {
    STATS: {
        MIN: 0,
        MAX: 100,
    },
    MONEY: {
        MAX_CASH: *********,
        MAX_BANK: ************,
    },
    APPEARANCE: {
        FACE_FEATURES: {
            MIN: -1.0,
            MAX: 1.0,
        },
        COLORS: {
            MIN: 0,
            MAX: 63,
        },
        HAIR_STYLES: {
            MALE: { MIN: 0, MAX: 36 },
            FEMALE: { MIN: 0, MAX: 38 },
        },
    },
};
// Error Messages
exports.ERROR_MESSAGES = {
    // Authentication
    INVALID_CREDENTIALS: 'Invalid username/email or password',
    USER_NOT_FOUND: 'User not found',
    USER_ALREADY_EXISTS: 'User already exists',
    EMAIL_ALREADY_EXISTS: 'Email already registered',
    USERNAME_ALREADY_EXISTS: 'Username already taken',
    INVALID_EMAIL: 'Invalid email address',
    WEAK_PASSWORD: 'Password must be at least 8 characters long',
    PASSWORDS_DONT_MATCH: 'Passwords do not match',
    EMAIL_NOT_VERIFIED: 'Please verify your email address',
    INVALID_VERIFICATION_CODE: 'Invalid or expired verification code',
    TOKEN_EXPIRED: 'Token has expired',
    INVALID_TOKEN: 'Invalid token',
    // Character
    CHARACTER_NOT_FOUND: 'Character not found',
    CHARACTER_LIMIT_REACHED: 'Maximum characters per account reached',
    INVALID_CHARACTER_NAME: 'Invalid character name',
    CHARACTER_NAME_TAKEN: 'Character name already taken',
    // General
    INTERNAL_SERVER_ERROR: 'Internal server error',
    UNAUTHORIZED: 'Unauthorized access',
    FORBIDDEN: 'Access forbidden',
    NOT_FOUND: 'Resource not found',
    BAD_REQUEST: 'Bad request',
    RATE_LIMIT_EXCEEDED: 'Too many requests, please try again later',
    VALIDATION_ERROR: 'Validation error',
    DATABASE_ERROR: 'Database error',
};
// Success Messages
exports.SUCCESS_MESSAGES = {
    // Authentication
    LOGIN_SUCCESS: 'Successfully logged in',
    REGISTER_SUCCESS: 'Account created successfully',
    LOGOUT_SUCCESS: 'Successfully logged out',
    EMAIL_VERIFIED: 'Email verified successfully',
    VERIFICATION_SENT: 'Verification email sent',
    PASSWORD_RESET_SENT: 'Password reset email sent',
    PASSWORD_RESET_SUCCESS: 'Password reset successfully',
    // Character
    CHARACTER_CREATED: 'Character created successfully',
    CHARACTER_UPDATED: 'Character updated successfully',
    CHARACTER_DELETED: 'Character deleted successfully',
    // General
    OPERATION_SUCCESS: 'Operation completed successfully',
    DATA_SAVED: 'Data saved successfully',
    DATA_UPDATED: 'Data updated successfully',
    DATA_DELETED: 'Data deleted successfully',
};
// Notification Types
exports.NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info',
};
// HTTP Status Codes
exports.HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
};
// Regex Patterns
exports.REGEX_PATTERNS = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
    CHARACTER_NAME: /^[a-zA-Z\s]{2,20}$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
};
