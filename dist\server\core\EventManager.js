"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventManager = void 0;
const events_1 = require("events");
const Logger_1 = require("../utils/Logger");
class EventManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.eventHandlers = new Map();
        this.logger = new Logger_1.Logger('EventManager');
        this.setMaxListeners(100); // Increase max listeners for complex event handling
    }
    // Register an event handler
    on(eventName, handler) {
        super.on(eventName, handler);
        if (!this.eventHandlers.has(eventName)) {
            this.eventHandlers.set(eventName, []);
        }
        this.eventHandlers.get(eventName).push(handler);
        this.logger.debug(`Registered handler for event: ${eventName}`);
        return this;
    }
    // Register a one-time event handler
    once(eventName, handler) {
        super.once(eventName, handler);
        this.logger.debug(`Registered one-time handler for event: ${eventName}`);
        return this;
    }
    // Remove an event handler
    off(eventName, handler) {
        super.off(eventName, handler);
        const handlers = this.eventHandlers.get(eventName);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
                if (handlers.length === 0) {
                    this.eventHandlers.delete(eventName);
                }
            }
        }
        this.logger.debug(`Removed handler for event: ${eventName}`);
        return this;
    }
    // Emit an event with error handling
    emit(eventName, ...args) {
        try {
            this.logger.debug(`Emitting event: ${eventName}`, args.length > 0 ? args : '');
            return super.emit(eventName, ...args);
        }
        catch (error) {
            this.logger.error(`Error emitting event ${eventName}:`, error);
            return false;
        }
    }
    // Emit an event asynchronously with error handling
    async emitAsync(eventName, data) {
        const handlers = this.eventHandlers.get(eventName) || [];
        if (handlers.length === 0) {
            this.logger.debug(`No handlers found for event: ${eventName}`);
            return;
        }
        this.logger.debug(`Emitting async event: ${eventName} to ${handlers.length} handlers`);
        const promises = handlers.map(async (handler) => {
            try {
                await handler(data);
            }
            catch (error) {
                this.logger.error(`Error in async handler for event ${eventName}:`, error);
            }
        });
        await Promise.allSettled(promises);
    }
    // Get all registered events
    getRegisteredEvents() {
        return Array.from(this.eventHandlers.keys());
    }
    // Get handler count for an event
    getHandlerCount(eventName) {
        return this.eventHandlers.get(eventName)?.length || 0;
    }
    // Remove all handlers for an event
    removeAllListeners(eventName) {
        if (eventName) {
            super.removeAllListeners(eventName);
            this.eventHandlers.delete(eventName);
            this.logger.debug(`Removed all handlers for event: ${eventName}`);
        }
        else {
            super.removeAllListeners();
            this.eventHandlers.clear();
            this.logger.debug('Removed all event handlers');
        }
        return this;
    }
    // Get event statistics
    getStats() {
        const events = {};
        let totalHandlers = 0;
        for (const [eventName, handlers] of this.eventHandlers) {
            events[eventName] = handlers.length;
            totalHandlers += handlers.length;
        }
        return {
            totalEvents: this.eventHandlers.size,
            totalHandlers,
            events,
        };
    }
    // Middleware support for events
    use(eventName, middleware) {
        const originalHandlers = this.eventHandlers.get(eventName) || [];
        // Wrap all existing handlers with middleware
        originalHandlers.forEach((handler, index) => {
            const wrappedHandler = async (data) => {
                await middleware(data);
                await handler(data);
            };
            originalHandlers[index] = wrappedHandler;
        });
        this.logger.debug(`Applied middleware to event: ${eventName}`);
        return this;
    }
}
exports.EventManager = EventManager;
