import { Logger } from '../utils/Logger';
import { DatabaseManager } from './DatabaseManager';
import { ConfigManager } from './ConfigManager';
import { EventManager } from './EventManager';
import { PlayerManager } from '../managers/PlayerManager';
import { AuthManager } from '../managers/AuthManager';
import { CharacterManager } from '../managers/CharacterManager';

export class Server {
  private logger: Logger;
  private config: ConfigManager;
  private database: DatabaseManager;
  private eventManager: EventManager;
  private playerManager: PlayerManager;
  private authManager: AuthManager;
  private characterManager: CharacterManager;
  private isInitialized = false;

  constructor() {
    this.logger = new Logger('Server');
    this.config = new ConfigManager();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Server is already initialized');
    }

    try {
      this.logger.info('Initializing server components...');

      // Initialize configuration
      await this.config.load();
      this.logger.success('Configuration loaded');

      // Initialize database
      this.database = new DatabaseManager(this.config);
      await this.database.connect();
      this.logger.success('Database connected');

      // Initialize event manager
      this.eventManager = new EventManager();
      this.logger.success('Event manager initialized');

      // Initialize managers
      this.authManager = new AuthManager(this.config, this.database);
      this.characterManager = new CharacterManager(this.database);
      this.playerManager = new PlayerManager(
        this.database,
        this.authManager,
        this.characterManager,
        this.eventManager
      );

      this.logger.success('Managers initialized');

      // Register event handlers
      this.registerEventHandlers();
      this.logger.success('Event handlers registered');

      // Setup RageMP events
      this.setupRageMPEvents();
      this.logger.success('RageMP events configured');

      this.isInitialized = true;
      this.logger.success('Server initialization complete');

    } catch (error) {
      this.logger.error('Failed to initialize server:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    this.logger.info('Shutting down server...');

    try {
      // Save all player data
      if (this.playerManager) {
        await this.playerManager.saveAllPlayers();
        this.logger.info('All player data saved');
      }

      // Disconnect from database
      if (this.database) {
        await this.database.disconnect();
        this.logger.info('Database disconnected');
      }

      this.logger.success('Server shutdown complete');
    } catch (error) {
      this.logger.error('Error during shutdown:', error);
      throw error;
    }
  }

  private registerEventHandlers(): void {
    // Authentication events
    this.eventManager.on('auth:login', this.authManager.handleLogin.bind(this.authManager));
    this.eventManager.on('auth:register', this.authManager.handleRegister.bind(this.authManager));
    this.eventManager.on('auth:logout', this.authManager.handleLogout.bind(this.authManager));
    this.eventManager.on('auth:verify-email', this.authManager.handleVerifyEmail.bind(this.authManager));

    // Character events
    this.eventManager.on('character:create', this.characterManager.handleCreate.bind(this.characterManager));
    this.eventManager.on('character:select', this.characterManager.handleSelect.bind(this.characterManager));
    this.eventManager.on('character:update', this.characterManager.handleUpdate.bind(this.characterManager));

    // Player events
    this.eventManager.on('player:spawn', this.playerManager.handleSpawn.bind(this.playerManager));
    this.eventManager.on('player:update', this.playerManager.handleUpdate.bind(this.playerManager));
    this.eventManager.on('player:save', this.playerManager.handleSave.bind(this.playerManager));
  }

  private setupRageMPEvents(): void {
    // Player connection events
    mp.events.add('playerJoin', (player: PlayerMp) => {
      this.playerManager.handlePlayerJoin(player);
    });

    mp.events.add('playerQuit', (player: PlayerMp, exitType: string, reason: string) => {
      this.playerManager.handlePlayerQuit(player, exitType, reason);
    });

    mp.events.add('playerSpawn', (player: PlayerMp) => {
      this.playerManager.handlePlayerSpawn(player);
    });

    mp.events.add('playerDeath', (player: PlayerMp, reason: number, killer: PlayerMp) => {
      this.playerManager.handlePlayerDeath(player, reason, killer);
    });

    // Chat events
    mp.events.add('playerChat', (player: PlayerMp, message: string) => {
      this.playerManager.handlePlayerChat(player, message);
    });

    // Custom events from client
    mp.events.add('server:event', (player: PlayerMp, eventName: string, data: any) => {
      this.eventManager.emit(eventName, { player, data });
    });

    this.logger.debug('RageMP events configured');
  }

  // Getters for accessing managers
  get configManager(): ConfigManager {
    return this.config;
  }

  get databaseManager(): DatabaseManager {
    return this.database;
  }

  get eventManagerInstance(): EventManager {
    return this.eventManager;
  }

  get playerManagerInstance(): PlayerManager {
    return this.playerManager;
  }

  get authManagerInstance(): AuthManager {
    return this.authManager;
  }

  get characterManagerInstance(): CharacterManager {
    return this.characterManager;
  }
}
