import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger';
import { EventHandler } from '../../types/index';

export class EventManager extends EventEmitter {
  private logger: Logger;
  private eventHandlers: Map<string, EventHandler[]> = new Map();

  constructor() {
    super();
    this.logger = new Logger('EventManager');
    this.setMaxListeners(100); // Increase max listeners for complex event handling
  }

  // Register an event handler
  on(eventName: string, handler: EventHandler): this {
    super.on(eventName, handler);
    
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, []);
    }
    this.eventHandlers.get(eventName)!.push(handler);
    
    this.logger.debug(`Registered handler for event: ${eventName}`);
    return this;
  }

  // Register a one-time event handler
  once(eventName: string, handler: EventHandler): this {
    super.once(eventName, handler);
    this.logger.debug(`Registered one-time handler for event: ${eventName}`);
    return this;
  }

  // Remove an event handler
  off(eventName: string, handler: EventHandler): this {
    super.off(eventName, handler);
    
    const handlers = this.eventHandlers.get(eventName);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        if (handlers.length === 0) {
          this.eventHandlers.delete(eventName);
        }
      }
    }
    
    this.logger.debug(`Removed handler for event: ${eventName}`);
    return this;
  }

  // Emit an event with error handling
  emit(eventName: string, ...args: any[]): boolean {
    try {
      this.logger.debug(`Emitting event: ${eventName}`, args.length > 0 ? args : '');
      return super.emit(eventName, ...args);
    } catch (error) {
      this.logger.error(`Error emitting event ${eventName}:`, error);
      return false;
    }
  }

  // Emit an event asynchronously with error handling
  async emitAsync(eventName: string, data?: any): Promise<void> {
    const handlers = this.eventHandlers.get(eventName) || [];
    
    if (handlers.length === 0) {
      this.logger.debug(`No handlers found for event: ${eventName}`);
      return;
    }

    this.logger.debug(`Emitting async event: ${eventName} to ${handlers.length} handlers`);

    const promises = handlers.map(async (handler) => {
      try {
        await handler(data);
      } catch (error) {
        this.logger.error(`Error in async handler for event ${eventName}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  // Get all registered events
  getRegisteredEvents(): string[] {
    return Array.from(this.eventHandlers.keys());
  }

  // Get handler count for an event
  getHandlerCount(eventName: string): number {
    return this.eventHandlers.get(eventName)?.length || 0;
  }

  // Remove all handlers for an event
  removeAllListeners(eventName?: string): this {
    if (eventName) {
      super.removeAllListeners(eventName);
      this.eventHandlers.delete(eventName);
      this.logger.debug(`Removed all handlers for event: ${eventName}`);
    } else {
      super.removeAllListeners();
      this.eventHandlers.clear();
      this.logger.debug('Removed all event handlers');
    }
    return this;
  }

  // Get event statistics
  getStats(): { totalEvents: number; totalHandlers: number; events: Record<string, number> } {
    const events: Record<string, number> = {};
    let totalHandlers = 0;

    for (const [eventName, handlers] of this.eventHandlers) {
      events[eventName] = handlers.length;
      totalHandlers += handlers.length;
    }

    return {
      totalEvents: this.eventHandlers.size,
      totalHandlers,
      events,
    };
  }

  // Middleware support for events
  use(eventName: string, middleware: EventHandler): this {
    const originalHandlers = this.eventHandlers.get(eventName) || [];
    
    // Wrap all existing handlers with middleware
    originalHandlers.forEach((handler, index) => {
      const wrappedHandler = async (data: any) => {
        await middleware(data);
        await handler(data);
      };
      originalHandlers[index] = wrappedHandler;
    });

    this.logger.debug(`Applied middleware to event: ${eventName}`);
    return this;
  }
}
