import React from 'react';
import ReactDOM from 'react-dom/client';
import { App } from './App';
import { GlobalStyles } from './styles/GlobalStyles';
import { ThemeProvider } from './providers/ThemeProvider';
import { AuthProvider } from './providers/AuthProvider';
import { NotificationProvider } from './providers/NotificationProvider';
import { EventProvider } from './providers/EventProvider';
import './styles/index.scss';

// Initialize the React application
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <ThemeProvider>
      <GlobalStyles />
      <EventProvider>
        <AuthProvider>
          <NotificationProvider>
            <App />
          </NotificationProvider>
        </AuthProvider>
      </EventProvider>
    </ThemeProvider>
  </React.StrictMode>
);

// Initialize RageMP client-side functionality
if (typeof mp !== 'undefined') {
  // RageMP is available, set up client-side events
  mp.events.add('client:ready', () => {
    console.log('Client is ready');
  });

  // Handle server events
  mp.events.add('ui:show', (componentType: string, data?: any) => {
    // This will be handled by the React app through the event system
    window.dispatchEvent(new CustomEvent('ragemp:ui:show', {
      detail: { componentType, data }
    }));
  });

  mp.events.add('ui:hide', (componentType: string) => {
    window.dispatchEvent(new CustomEvent('ragemp:ui:hide', {
      detail: { componentType }
    }));
  });

  mp.events.add('notification:show', (notification: any) => {
    window.dispatchEvent(new CustomEvent('ragemp:notification:show', {
      detail: notification
    }));
  });

  // Notify server that client is ready
  mp.events.callRemote('client:ready');
} else {
  // Development mode - RageMP not available
  console.log('Running in development mode - RageMP not available');
}
