import React, { createContext, useContext, useState, useEffect } from 'react';
import { Player, AuthResponse } from '../../types/index';

interface AuthContextType {
  isAuthenticated: boolean;
  user: Player | null;
  login: (credentials: { username: string; password: string; rememberMe?: boolean }) => Promise<AuthResponse>;
  register: (data: { username: string; email: string; password: string; confirmPassword: string }) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  verifyEmail: (email: string, code: string) => Promise<AuthResponse>;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<Player | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing authentication on mount
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (token) {
          // TODO: Validate token with server
          // For now, just set as authenticated
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: { username: string; password: string; rememberMe?: boolean }): Promise<AuthResponse> => {
    try {
      setIsLoading(true);

      // Send login request to server via RageMP
      if (typeof mp !== 'undefined') {
        mp.events.callRemote('server:event', ['auth:login', credentials]);
        
        // Wait for response (this would be handled by event listener in real implementation)
        return new Promise((resolve) => {
          const handleLoginResponse = (response: AuthResponse) => {
            if (response.success) {
              setIsAuthenticated(true);
              setUser(response.player || null);
              
              if (response.token) {
                localStorage.setItem('authToken', response.token);
              }
              
              if (response.refreshToken) {
                localStorage.setItem('refreshToken', response.refreshToken);
              }
            }
            
            resolve(response);
            setIsLoading(false);
          };

          // In real implementation, this would be set up as an event listener
          setTimeout(() => {
            handleLoginResponse({
              success: true,
              message: 'Login successful',
              token: 'mock-token',
              player: {
                id: '1',
                socialClubId: 'mock-social-club-id',
                username: credentials.username,
                isVerified: true,
                createdAt: new Date(),
                lastLogin: new Date(),
              },
            });
          }, 1000);
        });
      } else {
        // Development mode - mock response
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const response: AuthResponse = {
          success: true,
          message: 'Login successful (dev mode)',
          token: 'dev-token',
          player: {
            id: '1',
            socialClubId: 'dev-social-club-id',
            username: credentials.username,
            isVerified: true,
            createdAt: new Date(),
            lastLogin: new Date(),
          },
        };

        if (response.success) {
          setIsAuthenticated(true);
          setUser(response.player || null);
          localStorage.setItem('authToken', response.token || '');
        }

        return response;
      }
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Login failed',
      };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: { username: string; email: string; password: string; confirmPassword: string }): Promise<AuthResponse> => {
    try {
      setIsLoading(true);

      // Send register request to server via RageMP
      if (typeof mp !== 'undefined') {
        mp.events.callRemote('server:event', ['auth:register', data]);
        
        // Wait for response
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              success: true,
              message: 'Registration successful. Please check your email for verification.',
            });
          }, 1000);
        });
      } else {
        // Development mode - mock response
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return {
          success: true,
          message: 'Registration successful (dev mode). Please check your email for verification.',
        };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: 'Registration failed',
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Send logout request to server
      if (typeof mp !== 'undefined') {
        mp.events.callRemote('server:event', ['auth:logout', {}]);
      }

      // Clear local state
      setIsAuthenticated(false);
      setUser(null);
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const verifyEmail = async (email: string, code: string): Promise<AuthResponse> => {
    try {
      setIsLoading(true);

      // Send verification request to server
      if (typeof mp !== 'undefined') {
        mp.events.callRemote('server:event', ['auth:verify-email', { email, code }]);
        
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              success: true,
              message: 'Email verified successfully',
            });
          }, 1000);
        });
      } else {
        // Development mode - mock response
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return {
          success: true,
          message: 'Email verified successfully (dev mode)',
        };
      }
    } catch (error) {
      console.error('Email verification error:', error);
      return {
        success: false,
        message: 'Email verification failed',
      };
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    login,
    register,
    logout,
    verifyEmail,
    isLoading,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
