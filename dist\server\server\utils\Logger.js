"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
class Logger {
    constructor(context) {
        this.context = context;
    }
    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const formattedArgs = args.length > 0 ? ' ' + args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ') : '';
        return `[${timestamp}] [${level}] [${this.context}] ${message}${formattedArgs}`;
    }
    info(message, ...args) {
        console.log('\x1b[36m%s\x1b[0m', this.formatMessage('INFO', message, ...args));
    }
    success(message, ...args) {
        console.log('\x1b[32m%s\x1b[0m', this.formatMessage('SUCCESS', message, ...args));
    }
    warn(message, ...args) {
        console.log('\x1b[33m%s\x1b[0m', this.formatMessage('WARN', message, ...args));
    }
    error(message, ...args) {
        console.log('\x1b[31m%s\x1b[0m', this.formatMessage('ERROR', message, ...args));
    }
    debug(message, ...args) {
        if (process.env.NODE_ENV === 'development') {
            console.log('\x1b[35m%s\x1b[0m', this.formatMessage('DEBUG', message, ...args));
        }
    }
}
exports.Logger = Logger;
