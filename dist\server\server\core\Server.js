"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = void 0;
const Logger_1 = require("../utils/Logger");
const DatabaseManager_1 = require("./DatabaseManager");
const ConfigManager_1 = require("./ConfigManager");
const EventManager_1 = require("./EventManager");
const PlayerManager_1 = require("../managers/PlayerManager");
const AuthManager_1 = require("../managers/AuthManager");
const CharacterManager_1 = require("../managers/CharacterManager");
class Server {
    constructor() {
        this.isInitialized = false;
        this.logger = new Logger_1.Logger('Server');
        this.config = new ConfigManager_1.ConfigManager();
    }
    async initialize() {
        if (this.isInitialized) {
            throw new Error('Server is already initialized');
        }
        try {
            this.logger.info('Initializing server components...');
            // Initialize configuration
            await this.config.load();
            this.logger.success('Configuration loaded');
            // Initialize database
            this.database = new DatabaseManager_1.DatabaseManager(this.config);
            await this.database.connect();
            this.logger.success('Database connected');
            // Initialize event manager
            this.eventManager = new EventManager_1.EventManager();
            this.logger.success('Event manager initialized');
            // Initialize managers
            this.authManager = new AuthManager_1.AuthManager(this.config, this.database);
            this.characterManager = new CharacterManager_1.CharacterManager(this.database);
            this.playerManager = new PlayerManager_1.PlayerManager(this.database, this.authManager, this.characterManager, this.eventManager);
            this.logger.success('Managers initialized');
            // Register event handlers
            this.registerEventHandlers();
            this.logger.success('Event handlers registered');
            // Setup RageMP events
            this.setupRageMPEvents();
            this.logger.success('RageMP events configured');
            this.isInitialized = true;
            this.logger.success('Server initialization complete');
        }
        catch (error) {
            this.logger.error('Failed to initialize server:', error);
            throw error;
        }
    }
    async shutdown() {
        this.logger.info('Shutting down server...');
        try {
            // Save all player data
            if (this.playerManager) {
                await this.playerManager.saveAllPlayers();
                this.logger.info('All player data saved');
            }
            // Disconnect from database
            if (this.database) {
                await this.database.disconnect();
                this.logger.info('Database disconnected');
            }
            this.logger.success('Server shutdown complete');
        }
        catch (error) {
            this.logger.error('Error during shutdown:', error);
            throw error;
        }
    }
    registerEventHandlers() {
        // Authentication events
        this.eventManager.on('auth:login', this.authManager.handleLogin.bind(this.authManager));
        this.eventManager.on('auth:register', this.authManager.handleRegister.bind(this.authManager));
        this.eventManager.on('auth:logout', this.authManager.handleLogout.bind(this.authManager));
        this.eventManager.on('auth:verify-email', this.authManager.handleVerifyEmail.bind(this.authManager));
        // Character events
        this.eventManager.on('character:create', this.characterManager.handleCreate.bind(this.characterManager));
        this.eventManager.on('character:select', this.characterManager.handleSelect.bind(this.characterManager));
        this.eventManager.on('character:update', this.characterManager.handleUpdate.bind(this.characterManager));
        // Player events
        this.eventManager.on('player:spawn', this.playerManager.handleSpawn.bind(this.playerManager));
        this.eventManager.on('player:update', this.playerManager.handleUpdate.bind(this.playerManager));
        this.eventManager.on('player:save', this.playerManager.handleSave.bind(this.playerManager));
    }
    setupRageMPEvents() {
        // Player connection events
        mp.events.add('playerJoin', (player) => {
            this.playerManager.handlePlayerJoin(player);
        });
        mp.events.add('playerQuit', (player, exitType, reason) => {
            this.playerManager.handlePlayerQuit(player, exitType, reason);
        });
        mp.events.add('playerSpawn', (player) => {
            this.playerManager.handlePlayerSpawn(player);
        });
        mp.events.add('playerDeath', (player, reason, killer) => {
            this.playerManager.handlePlayerDeath(player, reason, killer);
        });
        // Chat events
        mp.events.add('playerChat', (player, message) => {
            this.playerManager.handlePlayerChat(player, message);
        });
        // Custom events from client
        mp.events.add('server:event', (player, eventName, data) => {
            this.eventManager.emit(eventName, { player, data });
        });
        this.logger.debug('RageMP events configured');
    }
    // Getters for accessing managers
    get configManager() {
        return this.config;
    }
    get databaseManager() {
        return this.database;
    }
    get eventManagerInstance() {
        return this.eventManager;
    }
    get playerManagerInstance() {
        return this.playerManager;
    }
    get authManagerInstance() {
        return this.authManager;
    }
    get characterManagerInstance() {
        return this.characterManager;
    }
}
exports.Server = Server;
