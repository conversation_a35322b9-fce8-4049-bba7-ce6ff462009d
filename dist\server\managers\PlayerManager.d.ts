import { DatabaseManager } from '../core/DatabaseManager';
import { AuthManager } from './AuthManager';
import { CharacterManager } from './CharacterManager';
import { EventManager } from '../core/EventManager';
import { Player } from '@types/index';
export declare class PlayerManager {
    private logger;
    private database;
    private authManager;
    private characterManager;
    private eventManager;
    private connectedPlayers;
    constructor(database: DatabaseManager, authManager: AuthManager, characterManager: CharacterManager, eventManager: EventManager);
    handlePlayerJoin(player: PlayerMp): Promise<void>;
    handlePlayerQuit(player: PlayerMp, exitType: string, reason: string): Promise<void>;
    handlePlayerSpawn(player: PlayerMp): Promise<void>;
    handlePlayerDeath(player: PlayerMp, reason: number, killer: PlayerMp): Promise<void>;
    handlePlayerChat(player: PlayerMp, message: string): Promise<void>;
    handleSpawn(data: {
        player: Player;
        ragempPlayer: PlayerMp;
    }): Promise<void>;
    handleUpdate(data: {
        player: Player;
        updates: Partial<Player>;
    }): Promise<void>;
    handleSave(data: {
        player: Player;
    }): Promise<void>;
    private savePlayer;
    saveAllPlayers(): Promise<void>;
    getConnectedPlayer(playerId: string): Player | undefined;
    getConnectedPlayers(): Player[];
    getPlayerCount(): number;
}
