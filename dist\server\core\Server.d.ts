import { DatabaseManager } from './DatabaseManager';
import { ConfigManager } from './ConfigManager';
import { EventManager } from './EventManager';
import { PlayerManager } from '../managers/PlayerManager';
import { AuthManager } from '../managers/AuthManager';
import { CharacterManager } from '../managers/CharacterManager';
export declare class Server {
    private logger;
    private config;
    private database;
    private eventManager;
    private playerManager;
    private authManager;
    private characterManager;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    shutdown(): Promise<void>;
    private registerEventHandlers;
    private setupRageMPEvents;
    get configManager(): ConfigManager;
    get databaseManager(): DatabaseManager;
    get eventManagerInstance(): EventManager;
    get playerManagerInstance(): PlayerManager;
    get authManagerInstance(): AuthManager;
    get characterManagerInstance(): CharacterManager;
}
