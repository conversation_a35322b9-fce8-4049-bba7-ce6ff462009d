import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { NotificationData } from '../../../types/index';
import { useNotification } from '../../providers/NotificationProvider';

const slideIn = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const slideOut = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
`;

const NotificationWrapper = styled.div<{ isExiting: boolean; type: string }>`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => {
    switch (props.type) {
      case 'success': return props.theme.colors.status.success;
      case 'error': return props.theme.colors.status.error;
      case 'warning': return props.theme.colors.status.warning;
      case 'info': return props.theme.colors.status.info;
      default: return props.theme.colors.border;
    }
  }};
  border-left: 4px solid ${props => {
    switch (props.type) {
      case 'success': return props.theme.colors.status.success;
      case 'error': return props.theme.colors.status.error;
      case 'warning': return props.theme.colors.status.warning;
      case 'info': return props.theme.colors.status.info;
      default: return props.theme.colors.primary;
    }
  }};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  box-shadow: ${props => props.theme.shadows.lg};
  backdrop-filter: blur(10px);
  animation: ${props => props.isExiting ? slideOut : slideIn} 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const Title = styled.h4`
  color: ${props => props.theme.colors.text.primary};
  font-size: 16px;
  font-weight: 600;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${props => props.theme.colors.text.primary};
  }
`;

const Message = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
`;

const Actions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  margin-top: ${props => props.theme.spacing.md};
`;

const ActionButton = styled.button<{ variant: string }>`
  background: ${props => {
    switch (props.variant) {
      case 'primary': return props.theme.colors.primary;
      case 'secondary': return 'transparent';
      case 'danger': return props.theme.colors.status.error;
      default: return props.theme.colors.primary;
    }
  }};
  color: ${props => {
    switch (props.variant) {
      case 'secondary': return props.theme.colors.text.primary;
      default: return props.theme.colors.background;
    }
  }};
  border: ${props => props.variant === 'secondary' ? `1px solid ${props.theme.colors.border}` : 'none'};
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};

  &:hover {
    opacity: 0.8;
  }
`;

const ProgressBar = styled.div<{ duration: number }>`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: ${props => props.theme.colors.primary};
  animation: progress ${props => props.duration}ms linear;

  @keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
  }
`;

interface NotificationItemProps {
  notification: NotificationData;
}

export const NotificationItem: React.FC<NotificationItemProps> = ({ notification }) => {
  const [isExiting, setIsExiting] = useState(false);
  const { hideNotification } = useNotification();

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      hideNotification(notification.id);
    }, 300);
  };

  const handleAction = (action: string) => {
    // Handle notification action
    console.log('Notification action:', action);
    handleClose();
  };

  useEffect(() => {
    if (notification.duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, notification.duration);

      return () => clearTimeout(timer);
    }
  }, [notification.duration]);

  return (
    <NotificationWrapper isExiting={isExiting} type={notification.type}>
      <Header>
        <Title>{notification.title}</Title>
        <CloseButton onClick={handleClose}>×</CloseButton>
      </Header>
      
      <Message>{notification.message}</Message>
      
      {notification.actions && notification.actions.length > 0 && (
        <Actions>
          {notification.actions.map((action, index) => (
            <ActionButton
              key={index}
              variant={action.style || 'primary'}
              onClick={() => handleAction(action.action)}
            >
              {action.label}
            </ActionButton>
          ))}
        </Actions>
      )}
      
      {notification.duration > 0 && (
        <ProgressBar duration={notification.duration} />
      )}
    </NotificationWrapper>
  );
};
