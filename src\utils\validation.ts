import { REGEX_PATTERNS, GAME_CONSTANTS, ERROR_MESSAGES } from '../shared/constants';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export class Validator {
  private errors: string[] = [];

  constructor() {
    this.reset();
  }

  reset(): void {
    this.errors = [];
  }

  getResult(): ValidationResult {
    return {
      isValid: this.errors.length === 0,
      errors: [...this.errors],
    };
  }

  private addError(message: string): this {
    this.errors.push(message);
    return this;
  }

  // Email validation
  email(value: string, required = true): this {
    if (!required && !value) return this;
    
    if (!value) {
      return this.addError('Email is required');
    }

    if (!REGEX_PATTERNS.EMAIL.test(value)) {
      return this.addError(ERROR_MESSAGES.INVALID_EMAIL);
    }

    return this;
  }

  // Username validation
  username(value: string, required = true): this {
    if (!required && !value) return this;

    if (!value) {
      return this.addError('Username is required');
    }

    if (value.length < GAME_CONSTANTS.MIN_USERNAME_LENGTH) {
      return this.addError(`Username must be at least ${GAME_CONSTANTS.MIN_USERNAME_LENGTH} characters long`);
    }

    if (value.length > GAME_CONSTANTS.MAX_USERNAME_LENGTH) {
      return this.addError(`Username must be no more than ${GAME_CONSTANTS.MAX_USERNAME_LENGTH} characters long`);
    }

    if (!REGEX_PATTERNS.USERNAME.test(value)) {
      return this.addError('Username can only contain letters, numbers, and underscores');
    }

    return this;
  }

  // Password validation
  password(value: string, required = true): this {
    if (!required && !value) return this;

    if (!value) {
      return this.addError('Password is required');
    }

    if (value.length < GAME_CONSTANTS.PASSWORD_MIN_LENGTH) {
      return this.addError(ERROR_MESSAGES.WEAK_PASSWORD);
    }

    if (!REGEX_PATTERNS.PASSWORD.test(value)) {
      return this.addError('Password must contain at least one uppercase letter, one lowercase letter, and one number');
    }

    return this;
  }

  // Password confirmation validation
  passwordConfirmation(password: string, confirmation: string): this {
    if (password !== confirmation) {
      return this.addError(ERROR_MESSAGES.PASSWORDS_DONT_MATCH);
    }

    return this;
  }

  // Character name validation
  characterName(value: string, required = true): this {
    if (!required && !value) return this;

    if (!value) {
      return this.addError('Character name is required');
    }

    if (value.length < GAME_CONSTANTS.MIN_CHARACTER_NAME_LENGTH) {
      return this.addError(`Character name must be at least ${GAME_CONSTANTS.MIN_CHARACTER_NAME_LENGTH} characters long`);
    }

    if (value.length > GAME_CONSTANTS.MAX_CHARACTER_NAME_LENGTH) {
      return this.addError(`Character name must be no more than ${GAME_CONSTANTS.MAX_CHARACTER_NAME_LENGTH} characters long`);
    }

    if (!REGEX_PATTERNS.CHARACTER_NAME.test(value)) {
      return this.addError('Character name can only contain letters and spaces');
    }

    return this;
  }

  // Date validation
  date(value: string | Date, required = true): this {
    if (!required && !value) return this;

    if (!value) {
      return this.addError('Date is required');
    }

    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return this.addError('Invalid date format');
    }

    return this;
  }

  // Age validation (for character creation)
  age(dateOfBirth: string | Date, minAge = 18, maxAge = 80): this {
    const birthDate = new Date(dateOfBirth);
    if (isNaN(birthDate.getTime())) {
      return this.addError('Invalid date of birth');
    }

    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      // Haven't had birthday this year yet
    }

    if (age < minAge) {
      return this.addError(`Character must be at least ${minAge} years old`);
    }

    if (age > maxAge) {
      return this.addError(`Character cannot be older than ${maxAge} years`);
    }

    return this;
  }

  // Gender validation
  gender(value: string, required = true): this {
    if (!required && !value) return this;

    if (!value) {
      return this.addError('Gender is required');
    }

    if (!['male', 'female'].includes(value.toLowerCase())) {
      return this.addError('Gender must be either male or female');
    }

    return this;
  }

  // Number validation
  number(value: number | string, min?: number, max?: number, required = true): this {
    if (!required && (value === undefined || value === null || value === '')) return this;

    const num = typeof value === 'string' ? parseFloat(value) : value;

    if (isNaN(num)) {
      return this.addError('Value must be a valid number');
    }

    if (min !== undefined && num < min) {
      return this.addError(`Value must be at least ${min}`);
    }

    if (max !== undefined && num > max) {
      return this.addError(`Value must be no more than ${max}`);
    }

    return this;
  }

  // String length validation
  stringLength(value: string, min?: number, max?: number, required = true): this {
    if (!required && !value) return this;

    if (!value) {
      return this.addError('Value is required');
    }

    if (min !== undefined && value.length < min) {
      return this.addError(`Value must be at least ${min} characters long`);
    }

    if (max !== undefined && value.length > max) {
      return this.addError(`Value must be no more than ${max} characters long`);
    }

    return this;
  }

  // Array validation
  array(value: any[], min?: number, max?: number, required = true): this {
    if (!required && (!value || value.length === 0)) return this;

    if (!Array.isArray(value)) {
      return this.addError('Value must be an array');
    }

    if (min !== undefined && value.length < min) {
      return this.addError(`Array must contain at least ${min} items`);
    }

    if (max !== undefined && value.length > max) {
      return this.addError(`Array must contain no more than ${max} items`);
    }

    return this;
  }

  // Custom validation
  custom(value: any, validator: (value: any) => boolean | string, errorMessage?: string): this {
    const result = validator(value);
    
    if (result === false) {
      return this.addError(errorMessage || 'Validation failed');
    }
    
    if (typeof result === 'string') {
      return this.addError(result);
    }

    return this;
  }
}

// Utility functions for quick validation
export const validateEmail = (email: string): ValidationResult => {
  const validator = new Validator();
  return validator.email(email).getResult();
};

export const validateUsername = (username: string): ValidationResult => {
  const validator = new Validator();
  return validator.username(username).getResult();
};

export const validatePassword = (password: string): ValidationResult => {
  const validator = new Validator();
  return validator.password(password).getResult();
};

export const validateCharacterName = (name: string): ValidationResult => {
  const validator = new Validator();
  return validator.characterName(name).getResult();
};

export const validateRegistration = (data: {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
}): ValidationResult => {
  const validator = new Validator();
  return validator
    .email(data.email)
    .username(data.username)
    .password(data.password)
    .passwordConfirmation(data.password, data.confirmPassword)
    .getResult();
};

export const validateCharacterCreation = (data: {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
}): ValidationResult => {
  const validator = new Validator();
  return validator
    .characterName(data.firstName)
    .characterName(data.lastName)
    .date(data.dateOfBirth)
    .age(data.dateOfBirth)
    .gender(data.gender)
    .getResult();
};
