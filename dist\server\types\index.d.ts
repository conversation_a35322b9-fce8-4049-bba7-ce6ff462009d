export interface Player {
    id: string;
    socialClubId: string;
    username: string;
    email?: string;
    discordId?: string;
    googleId?: string;
    isVerified: boolean;
    createdAt: Date;
    lastLogin: Date;
    character?: Character;
}
export interface Character {
    id: string;
    playerId: string;
    firstName: string;
    lastName: string;
    dateOfBirth: Date;
    gender: 'male' | 'female';
    appearance: CharacterAppearance;
    stats: CharacterStats;
    position: Position;
    money: Money;
    inventory: InventoryItem[];
    createdAt: Date;
    updatedAt: Date;
}
export interface CharacterAppearance {
    faceFeatures: Record<string, number>;
    skinColor: number;
    eyeColor: number;
    hairStyle: number;
    hairColor: number;
    facialHair: number;
    facialHairColor: number;
    clothes: ClothingItem[];
    accessories: AccessoryItem[];
}
export interface CharacterStats {
    health: number;
    armor: number;
    hunger: number;
    thirst: number;
    stress: number;
    energy: number;
    experience: number;
    level: number;
}
export interface Position {
    x: number;
    y: number;
    z: number;
    heading: number;
    dimension: number;
}
export interface Money {
    cash: number;
    bank: number;
}
export interface InventoryItem {
    id: string;
    itemId: string;
    quantity: number;
    metadata?: Record<string, any>;
    slot: number;
}
export interface ClothingItem {
    component: number;
    drawable: number;
    texture: number;
}
export interface AccessoryItem {
    component: number;
    drawable: number;
    texture: number;
}
export interface AuthRequest {
    email?: string;
    username?: string;
    password: string;
    rememberMe?: boolean;
}
export interface RegisterRequest extends AuthRequest {
    email: string;
    username: string;
    confirmPassword: string;
}
export interface AuthResponse {
    success: boolean;
    message: string;
    token?: string;
    refreshToken?: string;
    player?: Player;
}
export interface VerificationRequest {
    email: string;
    code: string;
}
export interface UIComponent {
    id: string;
    type: string;
    visible: boolean;
    data?: Record<string, any>;
}
export interface NotificationData {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    duration?: number;
    actions?: NotificationAction[];
}
export interface NotificationAction {
    label: string;
    action: string;
    style?: 'primary' | 'secondary' | 'danger';
}
export interface ServerEvent {
    name: string;
    data?: any;
}
export interface ClientEvent {
    name: string;
    data?: any;
}
export interface DatabaseModel {
    _id?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface PlayerModel extends DatabaseModel {
    socialClubId: string;
    username: string;
    email?: string;
    passwordHash?: string;
    discordId?: string;
    googleId?: string;
    isVerified: boolean;
    verificationCode?: string;
    verificationExpires?: Date;
    refreshTokens: string[];
    lastLogin: Date;
    ipAddress?: string;
    hwid?: string;
}
export interface CharacterModel extends DatabaseModel {
    playerId: string;
    firstName: string;
    lastName: string;
    dateOfBirth: Date;
    gender: 'male' | 'female';
    appearance: CharacterAppearance;
    stats: CharacterStats;
    position: Position;
    money: Money;
    inventory: InventoryItem[];
}
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    errors?: string[];
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
    };
}
export interface ServerConfig {
    database: {
        url: string;
        name: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
    };
    email: {
        host: string;
        port: number;
        user: string;
        pass: string;
        from: string;
        fromName: string;
    };
    oauth: {
        discord: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        };
        google: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        };
    };
    server: {
        port: number;
        ragempPort: number;
        clientUrl: string;
        serverUrl: string;
    };
    security: {
        bcryptRounds: number;
        rateLimitWindowMs: number;
        rateLimitMaxRequests: number;
        sessionSecret: string;
        cookieSecure: boolean;
        cookieMaxAge: number;
    };
}
export type EventHandler<T = any> = (data: T) => void | Promise<void>;
export type Middleware<T = any> = (data: T, next: () => void) => void | Promise<void>;
export type ValidationRule<T = any> = (value: T) => boolean | string;
