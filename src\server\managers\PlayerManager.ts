import { DatabaseManager } from '../core/DatabaseManager';
import { AuthManager } from './AuthManager';
import { CharacterManager } from './CharacterManager';
import { EventManager } from '../core/EventManager';
import { Logger } from '../utils/Logger';
import { Player } from '../../types/index';

export class PlayerManager {
  private logger: Logger;
  private database: DatabaseManager;
  private authManager: AuthManager;
  private characterManager: CharacterManager;
  private eventManager: EventManager;
  private connectedPlayers: Map<string, Player> = new Map();

  constructor(
    database: DatabaseManager,
    authManager: AuthManager,
    characterManager: CharacterManager,
    eventManager: EventManager
  ) {
    this.logger = new Logger('PlayerManager');
    this.database = database;
    this.authManager = authManager;
    this.characterManager = characterManager;
    this.eventManager = eventManager;
  }

  async handlePlayerJoin(player: <PERSON>Mp): Promise<void> {
    try {
      this.logger.info(`Player joining: ${player.name} (${player.socialClubName})`);
      
      // Create basic player data
      const playerData: Player = {
        id: player.id.toString(),
        socialClubId: player.socialClubName || '',
        username: player.name,
        isVerified: false,
        createdAt: new Date(),
        lastLogin: new Date(),
      };

      this.connectedPlayers.set(player.id.toString(), playerData);

      // Trigger authentication UI
      player.call('ui:show', ['login', { welcomeMessage: 'Welcome to Realistic Roleplay!' }]);
      
      this.eventManager.emit('player:connected', { player: playerData, ragempPlayer: player });
      
    } catch (error) {
      this.logger.error(`Error handling player join for ${player.name}:`, error);
    }
  }

  async handlePlayerQuit(player: PlayerMp, exitType: string, reason: string): Promise<void> {
    try {
      this.logger.info(`Player leaving: ${player.name} (${exitType}: ${reason})`);
      
      const playerData = this.connectedPlayers.get(player.id.toString());
      if (playerData) {
        // Save player data before disconnect
        await this.savePlayer(playerData);
        this.connectedPlayers.delete(player.id.toString());
      }

      this.eventManager.emit('player:disconnected', { 
        player: playerData, 
        ragempPlayer: player, 
        exitType, 
        reason 
      });
      
    } catch (error) {
      this.logger.error(`Error handling player quit for ${player.name}:`, error);
    }
  }

  async handlePlayerSpawn(player: PlayerMp): Promise<void> {
    try {
      this.logger.debug(`Player spawned: ${player.name}`);
      
      const playerData = this.connectedPlayers.get(player.id.toString());
      if (playerData && playerData.character) {
        // Set player position to character's last position
        const pos = playerData.character.position;
        player.position = new mp.Vector3(pos.x, pos.y, pos.z);
        player.heading = pos.heading;
        player.dimension = pos.dimension;
      }

      this.eventManager.emit('player:spawned', { player: playerData, ragempPlayer: player });
      
    } catch (error) {
      this.logger.error(`Error handling player spawn for ${player.name}:`, error);
    }
  }

  async handlePlayerDeath(player: PlayerMp, reason: number, killer: PlayerMp): Promise<void> {
    try {
      this.logger.info(`Player died: ${player.name} (reason: ${reason})`);
      
      const playerData = this.connectedPlayers.get(player.id.toString());
      const killerData = killer ? this.connectedPlayers.get(killer.id.toString()) : null;

      this.eventManager.emit('player:death', { 
        player: playerData, 
        ragempPlayer: player, 
        reason, 
        killer: killerData,
        killerRagempPlayer: killer 
      });
      
    } catch (error) {
      this.logger.error(`Error handling player death for ${player.name}:`, error);
    }
  }

  async handlePlayerChat(player: PlayerMp, message: string): Promise<void> {
    try {
      this.logger.debug(`Player chat: ${player.name}: ${message}`);
      
      const playerData = this.connectedPlayers.get(player.id.toString());
      
      // Check if it's a command
      if (message.startsWith('/')) {
        this.eventManager.emit('chat:command', { 
          player: playerData, 
          ragempPlayer: player, 
          command: message 
        });
      } else {
        this.eventManager.emit('chat:message', { 
          player: playerData, 
          ragempPlayer: player, 
          message 
        });
      }
      
    } catch (error) {
      this.logger.error(`Error handling player chat for ${player.name}:`, error);
    }
  }

  async handleSpawn(data: { player: Player; ragempPlayer: PlayerMp }): Promise<void> {
    try {
      // This is called from the event system
      this.logger.debug(`Handling spawn event for player: ${data.player.username}`);
      
      // Additional spawn logic can be added here
      
    } catch (error) {
      this.logger.error('Error in handleSpawn:', error);
    }
  }

  async handleUpdate(data: { player: Player; updates: Partial<Player> }): Promise<void> {
    try {
      this.logger.debug(`Updating player: ${data.player.username}`);
      
      // Update player data
      Object.assign(data.player, data.updates);
      this.connectedPlayers.set(data.player.id, data.player);
      
    } catch (error) {
      this.logger.error('Error in handleUpdate:', error);
    }
  }

  async handleSave(data: { player: Player }): Promise<void> {
    try {
      await this.savePlayer(data.player);
    } catch (error) {
      this.logger.error('Error in handleSave:', error);
    }
  }

  private async savePlayer(player: Player): Promise<void> {
    try {
      // This would save to database - placeholder for now
      this.logger.debug(`Saving player data for: ${player.username}`);
      // TODO: Implement database save logic
    } catch (error) {
      this.logger.error(`Error saving player ${player.username}:`, error);
    }
  }

  async saveAllPlayers(): Promise<void> {
    this.logger.info('Saving all connected players...');
    
    const savePromises = Array.from(this.connectedPlayers.values()).map(player => 
      this.savePlayer(player)
    );
    
    await Promise.allSettled(savePromises);
    this.logger.success(`Saved ${this.connectedPlayers.size} players`);
  }

  getConnectedPlayer(playerId: string): Player | undefined {
    return this.connectedPlayers.get(playerId);
  }

  getConnectedPlayers(): Player[] {
    return Array.from(this.connectedPlayers.values());
  }

  getPlayerCount(): number {
    return this.connectedPlayers.size;
  }
}
