import { ServerConfig } from '../../types/index';
export declare class ConfigManager {
    private logger;
    private config;
    constructor();
    load(): Promise<void>;
    private validateConfig;
    private getNestedValue;
    get(): ServerConfig;
    getDatabaseConfig(): {
        url: string;
        name: string;
    };
    getJwtConfig(): {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
    };
    getEmailConfig(): {
        host: string;
        port: number;
        user: string;
        pass: string;
        from: string;
        fromName: string;
    };
    getOAuthConfig(): {
        discord: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        };
        google: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        };
    };
    getServerConfig(): {
        port: number;
        ragempPort: number;
        clientUrl: string;
        serverUrl: string;
    };
    getSecurityConfig(): {
        bcryptRounds: number;
        rateLimitWindowMs: number;
        rateLimitMaxRequests: number;
        sessionSecret: string;
        cookieSecure: boolean;
        cookieMaxAge: number;
    };
}
