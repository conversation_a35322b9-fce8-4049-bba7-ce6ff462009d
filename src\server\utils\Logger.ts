export class Logger {
  private context: string;

  constructor(context: string) {
    this.context = context;
  }

  private formatMessage(level: string, message: string, ...args: any[]): string {
    const timestamp = new Date().toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] [${level}] [${this.context}] ${message}${formattedArgs}`;
  }

  info(message: string, ...args: any[]): void {
    console.log('\x1b[36m%s\x1b[0m', this.formatMessage('INFO', message, ...args));
  }

  success(message: string, ...args: any[]): void {
    console.log('\x1b[32m%s\x1b[0m', this.formatMessage('SUCCESS', message, ...args));
  }

  warn(message: string, ...args: any[]): void {
    console.log('\x1b[33m%s\x1b[0m', this.formatMessage('WARN', message, ...args));
  }

  error(message: string, ...args: any[]): void {
    console.log('\x1b[31m%s\x1b[0m', this.formatMessage('ERROR', message, ...args));
  }

  debug(message: string, ...args: any[]): void {
    if (process.env.NODE_ENV === 'development') {
      console.log('\x1b[35m%s\x1b[0m', this.formatMessage('DEBUG', message, ...args));
    }
  }
}
