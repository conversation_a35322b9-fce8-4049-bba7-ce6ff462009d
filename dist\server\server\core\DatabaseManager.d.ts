import mongoose from 'mongoose';
import { ConfigManager } from './ConfigManager';
export declare class DatabaseManager {
    private logger;
    private config;
    private isConnected;
    constructor(config: ConfigManager);
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    private setupEventListeners;
    getConnection(): mongoose.Connection;
    isHealthy(): boolean;
    ping(): Promise<boolean>;
}
