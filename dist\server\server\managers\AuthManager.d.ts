import { ConfigManager } from '../core/ConfigManager';
import { DatabaseManager } from '../core/DatabaseManager';
import { AuthRequest, RegisterRequest, VerificationRequest } from '../../types/index';
export declare class AuthManager {
    private logger;
    private config;
    private database;
    constructor(config: ConfigManager, database: DatabaseManager);
    handleLogin(data: {
        player: any;
        data: AuthRequest;
    }): Promise<void>;
    handleRegister(data: {
        player: any;
        data: RegisterRequest;
    }): Promise<void>;
    handleLogout(data: {
        player: any;
        data?: any;
    }): Promise<void>;
    handleVerifyEmail(data: {
        player: any;
        data: VerificationRequest;
    }): Promise<void>;
    generateVerificationCode(): Promise<string>;
    sendVerificationEmail(email: string, code: string): Promise<boolean>;
    validateToken(token: string): Promise<boolean>;
    refreshToken(refreshToken: string): Promise<string | null>;
}
