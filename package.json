{"name": "ragemp-realistic-roleplay", "version": "1.0.0", "description": "Highly realistic RageMP roleplay server with TypeScript and React", "main": "dist/server/index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon --exec ts-node src/server/index.ts", "dev:client": "webpack --mode development --watch", "build": "npm run build:server && npm run build:client", "build:server": "tsc -p tsconfig.server.json", "build:client": "webpack --mode production", "start": "node dist/server/index.js", "lint": "eslint src/**/*.{ts,tsx} --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss}", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["ragemp", "roleplay", "typescript", "react", "multiplayer", "gta5"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "concurrently": "^8.2.2", "css-loader": "^6.8.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "nodemon": "^3.0.2", "prettier": "^3.1.0", "sass": "^1.69.5", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-discord": "^0.1.4", "passport-google-oauth20": "^2.0.0", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^4.0.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.16", "styled-components": "^6.1.6", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2"}}