<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realistic Roleplay Server</title>
    <meta name="description" content="Highly realistic RageMP roleplay server with exceptional UI design">
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Critical CSS will be injected here by webpack -->
    <style>
        /* Critical loading styles */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            color: #ffffff;
            overflow: hidden;
        }
        
        #root {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        /* Loading spinner */
        .loading-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 20px;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="root">
        <!-- Loading screen shown while React loads -->
        <div class="loading-container" id="initial-loading">
            <div>
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading Realistic Roleplay...</div>
            </div>
        </div>
    </div>
    
    <!-- Remove loading screen once React takes over -->
    <script>
        window.addEventListener('DOMContentLoaded', () => {
            // Remove loading screen after a short delay to ensure React has mounted
            setTimeout(() => {
                const loadingElement = document.getElementById('initial-loading');
                if (loadingElement) {
                    loadingElement.style.opacity = '0';
                    loadingElement.style.transition = 'opacity 0.5s ease-out';
                    setTimeout(() => {
                        loadingElement.remove();
                    }, 500);
                }
            }, 1000);
        });
    </script>
</body>
</html>
