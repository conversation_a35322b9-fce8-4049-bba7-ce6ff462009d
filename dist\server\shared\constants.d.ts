export declare const SERVER_EVENTS: {
    readonly AUTH_LOGIN: "auth:login";
    readonly AUTH_REGISTER: "auth:register";
    readonly AUTH_LOGOUT: "auth:logout";
    readonly AUTH_VERIFY_EMAIL: "auth:verify-email";
    readonly AUTH_RESEND_VERIFICATION: "auth:resend-verification";
    readonly AUTH_FORGOT_PASSWORD: "auth:forgot-password";
    readonly AUTH_RESET_PASSWORD: "auth:reset-password";
    readonly AUTH_REFRESH_TOKEN: "auth:refresh-token";
    readonly PLAYER_CONNECT: "player:connect";
    readonly PLAYER_DISCONNECT: "player:disconnect";
    readonly PLAYER_SPAWN: "player:spawn";
    readonly PLAYER_UPDATE: "player:update";
    readonly PLAYER_SAVE: "player:save";
    readonly CHARACTER_CREATE: "character:create";
    readonly CHARACTER_SELECT: "character:select";
    readonly CHARACTER_UPDATE: "character:update";
    readonly CHARACTER_DELETE: "character:delete";
    readonly CHARACTER_SAVE: "character:save";
    readonly UI_SHOW: "ui:show";
    readonly UI_HIDE: "ui:hide";
    readonly UI_UPDATE: "ui:update";
    readonly UI_TOGGLE: "ui:toggle";
    readonly NOTIFICATION_SHOW: "notification:show";
    readonly NOTIFICATION_HIDE: "notification:hide";
    readonly NOTIFICATION_ACTION: "notification:action";
    readonly CHAT_MESSAGE: "chat:message";
    readonly CHAT_COMMAND: "chat:command";
    readonly CHAT_OOC: "chat:ooc";
    readonly CHAT_LOCAL: "chat:local";
    readonly CHAT_WHISPER: "chat:whisper";
    readonly CHAT_SHOUT: "chat:shout";
    readonly INVENTORY_OPEN: "inventory:open";
    readonly INVENTORY_CLOSE: "inventory:close";
    readonly INVENTORY_USE_ITEM: "inventory:use-item";
    readonly INVENTORY_DROP_ITEM: "inventory:drop-item";
    readonly INVENTORY_GIVE_ITEM: "inventory:give-item";
    readonly INVENTORY_UPDATE: "inventory:update";
    readonly VEHICLE_ENTER: "vehicle:enter";
    readonly VEHICLE_EXIT: "vehicle:exit";
    readonly VEHICLE_ENGINE_TOGGLE: "vehicle:engine-toggle";
    readonly VEHICLE_LOCK_TOGGLE: "vehicle:lock-toggle";
    readonly VEHICLE_SPAWN: "vehicle:spawn";
    readonly VEHICLE_DESPAWN: "vehicle:despawn";
    readonly ECONOMY_TRANSACTION: "economy:transaction";
    readonly ECONOMY_ATM_ACCESS: "economy:atm-access";
    readonly ECONOMY_BANK_TRANSFER: "economy:bank-transfer";
    readonly ECONOMY_PAY_PLAYER: "economy:pay-player";
    readonly JOB_START: "job:start";
    readonly JOB_STOP: "job:stop";
    readonly JOB_COMPLETE_TASK: "job:complete-task";
    readonly JOB_UPDATE_PROGRESS: "job:update-progress";
    readonly ADMIN_TELEPORT: "admin:teleport";
    readonly ADMIN_KICK_PLAYER: "admin:kick-player";
    readonly ADMIN_BAN_PLAYER: "admin:ban-player";
    readonly ADMIN_UNBAN_PLAYER: "admin:unban-player";
    readonly ADMIN_SET_MONEY: "admin:set-money";
    readonly ADMIN_GIVE_ITEM: "admin:give-item";
};
export declare const CLIENT_EVENTS: {
    readonly UI_READY: "ui:ready";
    readonly UI_INTERACTION: "ui:interaction";
    readonly UI_FORM_SUBMIT: "ui:form-submit";
    readonly UI_BUTTON_CLICK: "ui:button-click";
    readonly INPUT_KEY_PRESS: "input:key-press";
    readonly INPUT_KEY_RELEASE: "input:key-release";
    readonly INPUT_MOUSE_CLICK: "input:mouse-click";
    readonly CAMERA_CHANGE: "camera:change";
    readonly CAMERA_RESET: "camera:reset";
    readonly AUDIO_PLAY: "audio:play";
    readonly AUDIO_STOP: "audio:stop";
    readonly AUDIO_SET_VOLUME: "audio:set-volume";
};
export declare const UI_COMPONENTS: {
    readonly LOGIN: "login";
    readonly REGISTER: "register";
    readonly CHARACTER_CREATOR: "character-creator";
    readonly CHARACTER_SELECTOR: "character-selector";
    readonly HUD: "hud";
    readonly INVENTORY: "inventory";
    readonly CHAT: "chat";
    readonly PHONE: "phone";
    readonly ATM: "atm";
    readonly VEHICLE_HUD: "vehicle-hud";
    readonly ADMIN_PANEL: "admin-panel";
    readonly NOTIFICATION: "notification";
    readonly LOADING: "loading";
    readonly MENU: "menu";
    readonly DIALOG: "dialog";
};
export declare const GAME_CONSTANTS: {
    readonly MAX_PLAYERS: 100;
    readonly MAX_CHARACTERS_PER_PLAYER: 1;
    readonly DEFAULT_SPAWN_POSITION: {
        readonly x: -1037.8;
        readonly y: -2737.8;
        readonly z: 20.2;
        readonly heading: 0;
    };
    readonly MAX_INVENTORY_SLOTS: 50;
    readonly MAX_CHAT_MESSAGE_LENGTH: 255;
    readonly MAX_USERNAME_LENGTH: 20;
    readonly MIN_USERNAME_LENGTH: 3;
    readonly MAX_CHARACTER_NAME_LENGTH: 20;
    readonly MIN_CHARACTER_NAME_LENGTH: 2;
    readonly PASSWORD_MIN_LENGTH: 8;
    readonly VERIFICATION_CODE_LENGTH: 6;
    readonly VERIFICATION_CODE_EXPIRY: number;
};
export declare const CHARACTER_LIMITS: {
    readonly STATS: {
        readonly MIN: 0;
        readonly MAX: 100;
    };
    readonly MONEY: {
        readonly MAX_CASH: *********;
        readonly MAX_BANK: ************;
    };
    readonly APPEARANCE: {
        readonly FACE_FEATURES: {
            readonly MIN: -1;
            readonly MAX: 1;
        };
        readonly COLORS: {
            readonly MIN: 0;
            readonly MAX: 63;
        };
        readonly HAIR_STYLES: {
            readonly MALE: {
                readonly MIN: 0;
                readonly MAX: 36;
            };
            readonly FEMALE: {
                readonly MIN: 0;
                readonly MAX: 38;
            };
        };
    };
};
export declare const ERROR_MESSAGES: {
    readonly INVALID_CREDENTIALS: "Invalid username/email or password";
    readonly USER_NOT_FOUND: "User not found";
    readonly USER_ALREADY_EXISTS: "User already exists";
    readonly EMAIL_ALREADY_EXISTS: "Email already registered";
    readonly USERNAME_ALREADY_EXISTS: "Username already taken";
    readonly INVALID_EMAIL: "Invalid email address";
    readonly WEAK_PASSWORD: "Password must be at least 8 characters long";
    readonly PASSWORDS_DONT_MATCH: "Passwords do not match";
    readonly EMAIL_NOT_VERIFIED: "Please verify your email address";
    readonly INVALID_VERIFICATION_CODE: "Invalid or expired verification code";
    readonly TOKEN_EXPIRED: "Token has expired";
    readonly INVALID_TOKEN: "Invalid token";
    readonly CHARACTER_NOT_FOUND: "Character not found";
    readonly CHARACTER_LIMIT_REACHED: "Maximum characters per account reached";
    readonly INVALID_CHARACTER_NAME: "Invalid character name";
    readonly CHARACTER_NAME_TAKEN: "Character name already taken";
    readonly INTERNAL_SERVER_ERROR: "Internal server error";
    readonly UNAUTHORIZED: "Unauthorized access";
    readonly FORBIDDEN: "Access forbidden";
    readonly NOT_FOUND: "Resource not found";
    readonly BAD_REQUEST: "Bad request";
    readonly RATE_LIMIT_EXCEEDED: "Too many requests, please try again later";
    readonly VALIDATION_ERROR: "Validation error";
    readonly DATABASE_ERROR: "Database error";
};
export declare const SUCCESS_MESSAGES: {
    readonly LOGIN_SUCCESS: "Successfully logged in";
    readonly REGISTER_SUCCESS: "Account created successfully";
    readonly LOGOUT_SUCCESS: "Successfully logged out";
    readonly EMAIL_VERIFIED: "Email verified successfully";
    readonly VERIFICATION_SENT: "Verification email sent";
    readonly PASSWORD_RESET_SENT: "Password reset email sent";
    readonly PASSWORD_RESET_SUCCESS: "Password reset successfully";
    readonly CHARACTER_CREATED: "Character created successfully";
    readonly CHARACTER_UPDATED: "Character updated successfully";
    readonly CHARACTER_DELETED: "Character deleted successfully";
    readonly OPERATION_SUCCESS: "Operation completed successfully";
    readonly DATA_SAVED: "Data saved successfully";
    readonly DATA_UPDATED: "Data updated successfully";
    readonly DATA_DELETED: "Data deleted successfully";
};
export declare const NOTIFICATION_TYPES: {
    readonly SUCCESS: "success";
    readonly ERROR: "error";
    readonly WARNING: "warning";
    readonly INFO: "info";
};
export declare const HTTP_STATUS: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly NO_CONTENT: 204;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly CONFLICT: 409;
    readonly UNPROCESSABLE_ENTITY: 422;
    readonly TOO_MANY_REQUESTS: 429;
    readonly INTERNAL_SERVER_ERROR: 500;
};
export declare const REGEX_PATTERNS: {
    readonly EMAIL: RegExp;
    readonly USERNAME: RegExp;
    readonly CHARACTER_NAME: RegExp;
    readonly PASSWORD: RegExp;
};
