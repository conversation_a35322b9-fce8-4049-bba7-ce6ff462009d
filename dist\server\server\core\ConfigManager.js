"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const Logger_1 = require("../utils/Logger");
class ConfigManager {
    constructor() {
        this.logger = new Logger_1.Logger('ConfigManager');
    }
    async load() {
        try {
            this.config = {
                database: {
                    url: process.env.DATABASE_URL || 'mongodb://localhost:27017/ragemp_roleplay',
                    name: process.env.DATABASE_NAME || 'ragemp_roleplay',
                },
                jwt: {
                    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
                    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
                    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-refresh-token-secret',
                    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
                },
                email: {
                    host: process.env.SMTP_HOST || 'smtp.gmail.com',
                    port: parseInt(process.env.SMTP_PORT || '587'),
                    user: process.env.SMTP_USER || '',
                    pass: process.env.SMTP_PASS || '',
                    from: process.env.FROM_EMAIL || '<EMAIL>',
                    fromName: process.env.FROM_NAME || 'Your Roleplay Server',
                },
                oauth: {
                    discord: {
                        clientId: process.env.DISCORD_CLIENT_ID || '',
                        clientSecret: process.env.DISCORD_CLIENT_SECRET || '',
                        redirectUri: process.env.DISCORD_REDIRECT_URI || 'http://localhost:3000/auth/discord/callback',
                    },
                    google: {
                        clientId: process.env.GOOGLE_CLIENT_ID || '',
                        clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
                        redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/auth/google/callback',
                    },
                },
                server: {
                    port: parseInt(process.env.PORT || '3000'),
                    ragempPort: parseInt(process.env.RAGEMP_PORT || '22005'),
                    clientUrl: process.env.CLIENT_URL || 'http://localhost:3000',
                    serverUrl: process.env.SERVER_URL || 'http://localhost:3000',
                },
                security: {
                    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
                    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
                    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
                    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',
                    cookieSecure: process.env.COOKIE_SECURE === 'true',
                    cookieMaxAge: parseInt(process.env.COOKIE_MAX_AGE || '86400000'),
                },
            };
            this.validateConfig();
            this.logger.success('Configuration loaded successfully');
        }
        catch (error) {
            this.logger.error('Failed to load configuration:', error);
            throw error;
        }
    }
    validateConfig() {
        const requiredFields = [
            'database.url',
            'jwt.secret',
            'server.port',
        ];
        for (const field of requiredFields) {
            const value = this.getNestedValue(this.config, field);
            if (!value) {
                throw new Error(`Missing required configuration: ${field}`);
            }
        }
        // Warn about missing optional but important fields
        if (!this.config.email.user || !this.config.email.pass) {
            this.logger.warn('Email configuration is incomplete - email features will not work');
        }
        if (!this.config.oauth.discord.clientId || !this.config.oauth.discord.clientSecret) {
            this.logger.warn('Discord OAuth configuration is incomplete');
        }
        if (!this.config.oauth.google.clientId || !this.config.oauth.google.clientSecret) {
            this.logger.warn('Google OAuth configuration is incomplete');
        }
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    get() {
        if (!this.config) {
            throw new Error('Configuration not loaded. Call load() first.');
        }
        return this.config;
    }
    getDatabaseConfig() {
        return this.get().database;
    }
    getJwtConfig() {
        return this.get().jwt;
    }
    getEmailConfig() {
        return this.get().email;
    }
    getOAuthConfig() {
        return this.get().oauth;
    }
    getServerConfig() {
        return this.get().server;
    }
    getSecurityConfig() {
        return this.get().security;
    }
}
exports.ConfigManager = ConfigManager;
