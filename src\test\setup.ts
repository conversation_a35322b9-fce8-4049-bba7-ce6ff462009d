// Jest setup file for testing configuration

// Mock RageMP global objects for testing
global.mp = {
  events: {
    add: jest.fn(),
    call: jest.fn(),
    callRemote: jest.fn(),
    remove: jest.fn(),
  },
  players: {
    length: 0,
    at: jest.fn(),
    forEach: jest.fn(),
    toArray: jest.fn(() => []),
  },
  vehicles: {
    length: 0,
    at: jest.fn(),
    forEach: jest.fn(),
    toArray: jest.fn(() => []),
  },
} as any;

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup environment variables for testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'mongodb://localhost:27017/test_db';

// Mock fetch for API testing
global.fetch = jest.fn();

// Setup React Testing Library
import '@testing-library/jest-dom';
